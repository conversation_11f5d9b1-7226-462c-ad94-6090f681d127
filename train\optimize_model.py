#!/usr/bin/env python3
"""
Model optimization script to reduce model size for deployment
"""

import tensorflow as tf
import numpy as np
import os

def quantize_model(model_path, output_path):
    """Convert model to TensorFlow Lite with quantization (8-bit)"""
    print("🔄 Converting to TensorFlow Lite with quantization...")
    
    # Load the model
    model = tf.keras.models.load_model(model_path)
    
    # Convert to TensorFlow Lite with quantization
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    converter.optimizations = [tf.lite.Optimize.DEFAULT]
    
    # Optional: Use representative dataset for better quantization
    # converter.representative_dataset = representative_data_gen
    
    tflite_model = converter.convert()
    
    # Save the quantized model
    with open(output_path, 'wb') as f:
        f.write(tflite_model)
    
    # Compare sizes
    original_size = os.path.getsize(model_path) / (1024 * 1024)
    quantized_size = os.path.getsize(output_path) / (1024 * 1024)
    reduction = (1 - quantized_size / original_size) * 100
    
    print(f"✅ Quantization complete!")
    print(f"📊 Original size: {original_size:.1f} MB")
    print(f"📊 Quantized size: {quantized_size:.1f} MB")
    print(f"📉 Size reduction: {reduction:.1f}%")
    
    return output_path

def prune_model(model_path, output_path, target_sparsity=0.5):
    """Apply magnitude-based pruning to reduce model size"""
    print(f"🔄 Applying pruning with {target_sparsity*100}% sparsity...")
    
    try:
        import tensorflow_model_optimization as tfmot
    except ImportError:
        print("❌ tensorflow_model_optimization not installed")
        print("💡 Install with: pip install tensorflow-model-optimization")
        return None
    
    # Load model
    model = tf.keras.models.load_model(model_path)
    
    # Define pruning parameters
    pruning_params = {
        'pruning_schedule': tfmot.sparsity.keras.PolynomialDecay(
            initial_sparsity=0.0,
            final_sparsity=target_sparsity,
            begin_step=0,
            end_step=1000
        )
    }
    
    # Apply pruning to dense layers only
    def apply_pruning_to_dense(layer):
        if isinstance(layer, tf.keras.layers.Dense):
            return tfmot.sparsity.keras.prune_low_magnitude(layer, **pruning_params)
        return layer
    
    # Clone and modify model
    pruned_model = tf.keras.models.clone_model(
        model,
        clone_function=apply_pruning_to_dense,
    )
    
    pruned_model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Fine-tune the pruned model (you'd need training data for this)
    print("⚠️  Note: Pruned model needs fine-tuning with training data")
    
    # For now, just save the pruned architecture
    pruned_model.save(output_path)
    
    return output_path

def create_lightweight_model():
    """Create a much smaller model using MobileNetV2"""
    print("🔄 Creating lightweight model with MobileNetV2...")
    
    # Use MobileNetV2 instead of VGG16 (much smaller)
    base_model = tf.keras.applications.MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_shape=(224, 224, 3)
    )
    base_model.trainable = False
    
    # Smaller custom layers
    model = tf.keras.Sequential([
        base_model,
        tf.keras.layers.GlobalAveragePooling2D(),  # Instead of Flatten
        tf.keras.layers.Dense(128, activation='relu'),  # Reduced from 512
        tf.keras.layers.Dropout(0.3),
        tf.keras.layers.Dense(11, activation='softmax')  # 11 classes
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def create_tiny_model():
    """Create an even smaller custom CNN"""
    print("🔄 Creating tiny custom CNN...")
    
    model = tf.keras.Sequential([
        tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3)),
        tf.keras.layers.MaxPooling2D(2, 2),
        tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
        tf.keras.layers.MaxPooling2D(2, 2),
        tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
        tf.keras.layers.MaxPooling2D(2, 2),
        tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
        tf.keras.layers.MaxPooling2D(2, 2),
        tf.keras.layers.GlobalAveragePooling2D(),
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        tf.keras.layers.Dense(11, activation='softmax')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def main():
    """Main optimization function"""
    model_path = "../server/model.h5"
    
    if not os.path.exists(model_path):
        print("❌ Model not found at ../server/model.h5")
        print("💡 Run the download script first")
        return
    
    print("🚀 Starting model optimization...\n")
    
    # 1. Quantize existing model (fastest option)
    print("=" * 50)
    print("Option 1: Quantization (Recommended)")
    print("=" * 50)
    quantized_path = "model_quantized.tflite"
    quantize_model(model_path, quantized_path)
    
    # 2. Create lightweight alternatives
    print("\n" + "=" * 50)
    print("Option 2: Lightweight MobileNetV2 Model")
    print("=" * 50)
    lightweight_model = create_lightweight_model()
    lightweight_model.save("model_mobilenet.h5")
    
    lightweight_size = os.path.getsize("model_mobilenet.h5") / (1024 * 1024)
    print(f"📊 MobileNetV2 model size: {lightweight_size:.1f} MB")
    
    # 3. Create tiny model
    print("\n" + "=" * 50)
    print("Option 3: Tiny Custom CNN")
    print("=" * 50)
    tiny_model = create_tiny_model()
    tiny_model.save("model_tiny.h5")
    
    tiny_size = os.path.getsize("model_tiny.h5") / (1024 * 1024)
    print(f"📊 Tiny model size: {tiny_size:.1f} MB")
    
    # Summary
    original_size = os.path.getsize(model_path) / (1024 * 1024)
    print("\n" + "=" * 50)
    print("📊 SIZE COMPARISON")
    print("=" * 50)
    print(f"Original VGG16 model:     {original_size:.1f} MB")
    print(f"Quantized TFLite:         {os.path.getsize(quantized_path) / (1024 * 1024):.1f} MB")
    print(f"MobileNetV2 model:        {lightweight_size:.1f} MB")
    print(f"Tiny CNN model:           {tiny_size:.1f} MB")
    
    print("\n💡 Recommendations:")
    print("1. Use quantized TFLite for immediate deployment (smallest)")
    print("2. Retrain with MobileNetV2 for good balance of size/accuracy")
    print("3. Use tiny CNN if you need the smallest possible model")

if __name__ == "__main__":
    main()
