# C-Track Deployment Guide

## ✅ Setup Complete!

Your model has been successfully downloaded from Hugging Face and is ready for deployment!

### 📊 Model Information
- **Source**: `https://huggingface.co/georgebobby/c-track/blob/main/model.h5`
- **Size**: 204.77 MB
- **Architecture**: VGG-based CNN with custom classification head
- **Input Shape**: (224, 224, 3) - RGB images
- **Output Shape**: (11,) - 11 location classes
- **Classes**: Main Gate, PU Block, Architecture Block, Cross road, Block 1, Students Square, Open auditorium, Block 4, Xpress Cafe, Block 6, Amphi theater

## 🚀 Deployment to Render

### Option 1: Deploy from GitHub (Recommended)

1. **Push your code to GitHub** (if not already done):
   ```bash
   git add .
   git commit -m "Add model download and deployment setup"
   git push origin main
   ```

2. **Create a new Web Service on Render**:
   - Go to [render.com](https://render.com) and sign in
   - Click "New" → "Web Service"
   - Connect your GitHub repository
   - Select your `ctrack-locator` repository

3. **Configure the service**:
   - **Name**: `ctrack-locator-api`
   - **Root Directory**: `server`
   - **Runtime**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn --worker-class=gevent --workers=1 --timeout 180 --bind=0.0.0.0:$PORT index:app`

4. **Environment Variables** (Optional):
   - `PYTHON_VERSION`: `3.11`

### Option 2: Deploy using render.yaml

Your project already includes a `server/render.yaml` file. Simply:

1. Push to GitHub
2. On Render, choose "Deploy from repository"
3. Select your repo and Render will automatically use the YAML configuration

## 🔧 Local Testing

Before deploying, test your server locally:

```bash
# Start the Flask server
python server/index.py

# Test the health endpoint
curl http://localhost:5000/health

# Test prediction with an image
curl -X POST -F "image=@path/to/your/image.jpg" http://localhost:5000/predict
```

## 📁 Project Structure

```
ctrack-locator/
├── server/
│   ├── index.py              # Flask server with auto-download
│   ├── model.h5              # Downloaded from Hugging Face
│   ├── requirements.txt      # Python dependencies
│   ├── render.yaml          # Render deployment config
│   └── download_model.py    # Manual model download script
├── setup_for_deployment.py  # Complete setup script
└── DEPLOYMENT_GUIDE.md      # This guide
```

## 🔍 API Endpoints

### Health Check
```
GET /health
```
Response:
```json
{
  "status": "healthy",
  "model_loaded": true,
  "classes_loaded": 11
}
```

### Prediction
```
POST /predict
Content-Type: multipart/form-data
Body: image file
```
Response:
```json
{
  "predicted_class": "Main Gate",
  "confidence": 0.95,
  "top_predictions": {
    "Main Gate": 0.95,
    "PU Block": 0.03,
    "Block 1": 0.02
  },
  "all_probabilities": { ... }
}
```

## 🛠 Troubleshooting

### Model Download Issues
If the model doesn't download automatically:
```bash
python server/download_model.py
```

### Memory Issues on Render
- The model is ~205MB, ensure your Render plan supports this
- Consider using the "Standard" plan or higher for better performance

### Build Timeouts
- TensorFlow installation can take time
- Render's build timeout is usually sufficient, but you can contact support if needed

## 🔄 Updating the Model

To update your model on Hugging Face and redeploy:

1. **Update model on Hugging Face**
2. **Delete local model**: `rm server/model.h5`
3. **Redeploy**: The server will automatically download the latest version

## 📝 Next Steps

1. **Test locally** to ensure everything works
2. **Deploy to Render** using one of the methods above
3. **Update your Next.js frontend** to use the deployed API URL
4. **Test the full application** end-to-end

## 🎉 Success!

Your C-Track location detection model is now ready for production deployment!
