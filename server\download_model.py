#!/usr/bin/env python3
"""
Script to download the model from Hugging Face Hub
"""

import os
from huggingface_hub import hf_hub_download

def download_model():
    """Download the model from Hugging Face Hub"""
    
    # Your Hugging Face repository details
    repo_id = "georgebobby/c-track"
    filename = "model.h5"
    local_dir = "server"
    
    print(f"Downloading model from {repo_id}...")
    
    try:
        # Download the model file
        downloaded_path = hf_hub_download(
            repo_id=repo_id,
            filename=filename,
            local_dir=local_dir,
            local_dir_use_symlinks=False  # Download actual file, not symlink
        )
        
        print(f"✅ Model downloaded successfully to: {downloaded_path}")
        
        # Verify the file exists and has content
        if os.path.exists(downloaded_path):
            file_size = os.path.getsize(downloaded_path)
            print(f"📁 File size: {file_size / (1024*1024):.2f} MB")
        else:
            print("❌ Error: Downloaded file not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error downloading model: {str(e)}")
        print("\nTroubleshooting tips:")
        print("1. Make sure your repository 'georgebobby/c-track' is public")
        print("2. Verify the model file 'model.h5' exists in the repository")
        print("3. Check your internet connection")
        return False

if __name__ == "__main__":
    success = download_model()
    if success:
        print("\n🎉 Model ready for deployment!")
    else:
        print("\n💥 Model download failed. Please check the errors above.")
