#!/usr/bin/env python3
"""
Setup script for deployment - downloads model and verifies everything is ready
"""

import os
import sys
import subprocess
from pathlib import Path

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python requirements...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "server/requirements.txt"], 
                      check=True, capture_output=True, text=True)
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e.stderr}")
        return False

def download_model():
    """Download model using the download script"""
    print("🤖 Downloading model from Hugging Face...")
    try:
        subprocess.run([sys.executable, "server/download_model.py"], 
                      check=True, capture_output=True, text=True)
        print("✅ Model downloaded successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error downloading model: {e.stderr}")
        return False

def verify_setup():
    """Verify that everything is set up correctly"""
    print("🔍 Verifying setup...")
    
    # Check if model file exists
    model_path = Path("server/model.h5")
    if not model_path.exists():
        print("❌ Model file not found")
        return False
    
    # Check model file size
    file_size = model_path.stat().st_size
    if file_size < 1024:  # Less than 1KB is probably an error
        print(f"❌ Model file seems too small: {file_size} bytes")
        return False
    
    print(f"✅ Model file found: {file_size / (1024*1024):.2f} MB")
    
    # Check if requirements.txt exists
    if not Path("server/requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    print("✅ requirements.txt found")
    
    # Check if server script exists
    if not Path("server/index.py").exists():
        print("❌ server/index.py not found")
        return False
    
    print("✅ server/index.py found")
    
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up C-Track for deployment...\n")
    
    # Step 1: Install requirements
    if not install_requirements():
        print("💥 Setup failed at requirements installation")
        return False
    
    # Step 2: Download model
    if not download_model():
        print("💥 Setup failed at model download")
        return False
    
    # Step 3: Verify setup
    if not verify_setup():
        print("💥 Setup verification failed")
        return False
    
    print("\n🎉 Setup complete! Your project is ready for deployment.")
    print("\nNext steps:")
    print("1. Test locally: python server/index.py")
    print("2. Deploy to Render using the server/ directory")
    print("3. Make sure to set the start command to: python index.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
