#!/usr/bin/env python3
"""
Setup script to configure Git LFS for the model file
This is the recommended approach for production deployment
"""

import os
import subprocess
import sys

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        if result.stdout.strip():
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False

def check_git_lfs():
    """Check if Git LFS is installed"""
    try:
        subprocess.run(["git", "lfs", "version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def setup_git_lfs_for_model():
    """Set up Git LFS for the model file"""
    print("🚀 Setting up Git LFS for model deployment...\n")
    
    # Check if Git LFS is installed
    if not check_git_lfs():
        print("❌ Git LFS is not installed!")
        print("\n📦 Please install Git LFS first:")
        print("   Windows: Download from https://git-lfs.github.io/")
        print("   macOS: brew install git-lfs")
        print("   Linux: sudo apt install git-lfs")
        return False
    
    print("✅ Git LFS is installed")
    
    # Check if model exists
    if not os.path.exists("server/model.h5"):
        print("❌ Model file not found at server/model.h5")
        print("💡 Run 'python server/download_model.py' first")
        return False
    
    # Initialize Git LFS
    if not run_command("git lfs install", "Initializing Git LFS"):
        return False
    
    # Track .h5 files
    if not run_command("git lfs track '*.h5'", "Setting up LFS tracking for .h5 files"):
        return False
    
    # Add .gitattributes
    if not run_command("git add .gitattributes", "Adding .gitattributes"):
        return False
    
    # Check model file size
    model_size = os.path.getsize("server/model.h5") / (1024 * 1024)
    print(f"📁 Model file size: {model_size:.1f} MB")
    
    # Add model file
    if not run_command("git add server/model.h5", "Adding model file to Git LFS"):
        return False
    
    print("\n🎉 Git LFS setup complete!")
    print("\n📝 Next steps:")
    print("1. Commit the changes:")
    print("   git commit -m 'Add model with Git LFS'")
    print("\n2. Push to GitHub:")
    print("   git push origin main")
    print("\n3. Deploy to Render:")
    print("   - Your model will be available immediately")
    print("   - No download delays on startup")
    print("   - Faster cold starts")
    
    return True

def create_production_server():
    """Create a production server without download logic"""
    production_content = '''from flask import Flask, request, jsonify
from flask_cors import CORS
import tensorflow as tf
import numpy as np
from keras.preprocessing import image
import io
from PIL import Image
import os

app = Flask(__name__)
CORS(app)

# Configuration
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

# Load the model (should be available via Git LFS)
print("🔄 Loading model...")
model = tf.keras.models.load_model("server/model.h5", compile=False)
model.compile(optimizer="adam", loss="categorical_crossentropy", metrics=["accuracy"])
print("✅ Model loaded successfully!")

# Define class labels
class_labels = [
    'Main Gate', 'PU Block', 'Architecture Block',
    'Cross road', 'Block 1', 'Students Square',
    'Open auditorium', 'Block 4', 'Xpress Cafe',
    'Block 6', 'Amphi theater'
]

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/predict', methods=['POST'])
def predict():
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Unsupported file format'}), 400

    file.seek(0, os.SEEK_END)
    file_length = file.tell()
    file.seek(0)
    if file_length > MAX_FILE_SIZE:
        return jsonify({'error': 'File too large'}), 400

    try:
        img = Image.open(io.BytesIO(file.read()))
        if img.mode != 'RGB':
            img = img.convert('RGB')
        img = img.resize((224, 224))
        img_array = image.img_to_array(img) / 255.0
        img_array = np.expand_dims(img_array, axis=0)

        predictions = model.predict(img_array)
        predicted_class_idx = np.argmax(predictions, axis=1)[0]

        top3_indices = np.argsort(predictions[0])[-3:][::-1]
        top3_predictions = {
            class_labels[i]: float(predictions[0][i])
            for i in top3_indices
        }

        response = {
            'predicted_class': class_labels[predicted_class_idx],
            'confidence': float(predictions[0][predicted_class_idx]),
            'top_predictions': top3_predictions,
            'all_probabilities': {
                label: float(prob)
                for label, prob in zip(class_labels, predictions[0].tolist())
            }
        }
        return jsonify(response)

    except Exception as e:
        return jsonify({'error': 'Error processing image', 'details': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'model_loaded': True,
        'classes_loaded': len(class_labels)
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
'''
    
    with open("server/index_git_lfs.py", "w") as f:
        f.write(production_content)
    
    print("📝 Created server/index_git_lfs.py (production version)")

if __name__ == "__main__":
    success = setup_git_lfs_for_model()
    if success:
        create_production_server()
        print("\n🔧 Created optimized server file for Git LFS deployment")
    else:
        print("\n💥 Setup failed. Please resolve the issues above.")
