#!/usr/bin/env python3
"""
Convert existing model.h5 to TensorFlow Lite for smaller deployment
This can reduce size by 75% or more!
"""

import tensorflow as tf
import numpy as np
import os

def convert_to_tflite(model_path, output_path, quantize=True):
    """Convert Keras model to TensorFlow Lite"""
    
    print(f"🔄 Loading model from {model_path}...")
    model = tf.keras.models.load_model(model_path)
    
    # Create converter
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    
    if quantize:
        print("🔄 Applying quantization (8-bit)...")
        # Apply quantization for smaller size
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        
        # Optional: Use representative dataset for better quantization
        # This would require sample data from your training set
        # converter.representative_dataset = representative_data_gen
        
        # For even more aggressive quantization (may reduce accuracy)
        # converter.target_spec.supported_types = [tf.int8]
    
    # Convert the model
    print("🔄 Converting model...")
    tflite_model = converter.convert()
    
    # Save the converted model
    with open(output_path, 'wb') as f:
        f.write(tflite_model)
    
    # Compare sizes
    original_size = os.path.getsize(model_path) / (1024 * 1024)
    converted_size = os.path.getsize(output_path) / (1024 * 1024)
    reduction = (1 - converted_size / original_size) * 100
    
    print(f"✅ Conversion complete!")
    print(f"📊 Original size: {original_size:.1f} MB")
    print(f"📊 TFLite size: {converted_size:.1f} MB")
    print(f"📉 Size reduction: {reduction:.1f}%")
    
    return output_path

def test_tflite_model(tflite_path, test_image_path=None):
    """Test the TFLite model to ensure it works"""
    
    print(f"🔄 Testing TFLite model...")
    
    # Load TFLite model
    interpreter = tf.lite.Interpreter(model_path=tflite_path)
    interpreter.allocate_tensors()
    
    # Get input and output details
    input_details = interpreter.get_input_details()
    output_details = interpreter.get_output_details()
    
    print(f"📊 Input shape: {input_details[0]['shape']}")
    print(f"📊 Output shape: {output_details[0]['shape']}")
    
    # Create dummy test data if no test image provided
    if test_image_path and os.path.exists(test_image_path):
        from PIL import Image
        img = Image.open(test_image_path).convert('RGB')
        img = img.resize((224, 224))
        input_data = np.array(img, dtype=np.float32) / 255.0
        input_data = np.expand_dims(input_data, axis=0)
    else:
        # Use random data for testing
        input_shape = input_details[0]['shape']
        input_data = np.random.random(input_shape).astype(np.float32)
    
    # Run inference
    interpreter.set_tensor(input_details[0]['index'], input_data)
    interpreter.invoke()
    
    # Get output
    output_data = interpreter.get_tensor(output_details[0]['index'])
    
    print(f"✅ TFLite model test successful!")
    print(f"📊 Output shape: {output_data.shape}")
    
    return True

def create_tflite_server_code():
    """Create a Flask server that uses TFLite model"""
    
    server_code = '''#!/usr/bin/env python3
"""
Flask server using TensorFlow Lite model (much smaller!)
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import tensorflow as tf
import numpy as np
from PIL import Image
import io
import os

app = Flask(__name__)
CORS(app)

# Configuration
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

# Load TFLite model
print("🔄 Loading TensorFlow Lite model...")
interpreter = tf.lite.Interpreter(model_path="model_quantized.tflite")
interpreter.allocate_tensors()

# Get input and output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

print(f"✅ TFLite model loaded successfully!")
print(f"📊 Input shape: {input_details[0]['shape']}")
print(f"📊 Output shape: {output_details[0]['shape']}")

# Define class labels
class_labels = [
    'Main Gate', 'PU Block', 'Architecture Block',
    'Cross road', 'Block 1', 'Students Square',
    'Open auditorium', 'Block 4', 'Xpress Cafe',
    'Block 6', 'Amphi theater'
]

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/predict', methods=['POST'])
def predict():
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Unsupported file format'}), 400

    file.seek(0, os.SEEK_END)
    file_length = file.tell()
    file.seek(0)
    if file_length > MAX_FILE_SIZE:
        return jsonify({'error': 'File too large'}), 400

    try:
        # Preprocess image
        img = Image.open(io.BytesIO(file.read())).convert('RGB')
        img = img.resize((224, 224))
        img_array = np.array(img, dtype=np.float32) / 255.0
        img_array = np.expand_dims(img_array, axis=0)

        # Run inference with TFLite
        interpreter.set_tensor(input_details[0]['index'], img_array)
        interpreter.invoke()
        predictions = interpreter.get_tensor(output_details[0]['index'])[0]

        # Process results
        predicted_class_idx = np.argmax(predictions)
        top3_indices = np.argsort(predictions)[-3:][::-1]
        
        top3_predictions = {
            class_labels[i]: float(predictions[i])
            for i in top3_indices
        }

        response = {
            'predicted_class': class_labels[predicted_class_idx],
            'confidence': float(predictions[predicted_class_idx]),
            'top_predictions': top3_predictions,
            'all_probabilities': {
                label: float(prob)
                for label, prob in zip(class_labels, predictions.tolist())
            }
        }

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': 'Error processing image', 'details': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'model_type': 'TensorFlow Lite',
        'model_loaded': True,
        'classes_loaded': len(class_labels)
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
'''
    
    with open("../server/index_tflite.py", "w") as f:
        f.write(server_code)
    
    print("📝 Created ../server/index_tflite.py (TensorFlow Lite server)")

def main():
    """Main conversion function"""
    
    model_path = "../server/model.h5"
    
    if not os.path.exists(model_path):
        print("❌ Model not found at ../server/model.h5")
        print("💡 Make sure you've downloaded the model first")
        return
    
    print("🚀 Converting model to TensorFlow Lite...\n")
    
    # Convert to TFLite with quantization
    tflite_path = "model_quantized.tflite"
    convert_to_tflite(model_path, tflite_path, quantize=True)
    
    # Test the converted model
    print("\n" + "="*50)
    test_tflite_model(tflite_path)
    
    # Create TFLite server code
    print("\n" + "="*50)
    create_tflite_server_code()
    
    # Copy TFLite model to server directory
    import shutil
    server_tflite_path = "../server/model_quantized.tflite"
    shutil.copy(tflite_path, server_tflite_path)
    print(f"📁 Copied TFLite model to {server_tflite_path}")
    
    print("\n🎉 Conversion complete!")
    print("\n💡 Next steps:")
    print("1. Test the TFLite server: python ../server/index_tflite.py")
    print("2. Update your deployment to use the TFLite model")
    print("3. Enjoy much faster deployments with smaller model size!")

if __name__ == "__main__":
    main()
