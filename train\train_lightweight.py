#!/usr/bin/env python3
"""
Train a lightweight model for deployment
Much smaller than VGG16 but still effective
"""

import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2, EfficientNetB0
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Dropout
from tensorflow.keras.models import Model
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import os

# Define paths
dataset_path = "NN_images_processed"  # Replace with actual path

# Image parameters
img_size = (224, 224)
batch_size = 16

def create_mobilenet_model(num_classes):
    """Create a lightweight model using MobileNetV2"""
    print("🔄 Creating MobileNetV2-based model...")
    
    # MobileNetV2 is much smaller than VGG16 (~14MB vs ~138MB)
    base_model = MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_shape=(224, 224, 3)
    )
    base_model.trainable = False  # Freeze base model
    
    # Add lightweight custom layers
    model = tf.keras.Sequential([
        base_model,
        GlobalAveragePooling2D(),  # Much more efficient than Flatten
        Dense(128, activation='relu'),  # Reduced from 512
        Dropout(0.3),
        Dense(num_classes, activation='softmax')
    ])
    
    return model

def create_efficientnet_model(num_classes):
    """Create an even more efficient model using EfficientNetB0"""
    print("🔄 Creating EfficientNetB0-based model...")
    
    # EfficientNetB0 is very efficient
    base_model = EfficientNetB0(
        weights='imagenet',
        include_top=False,
        input_shape=(224, 224, 3)
    )
    base_model.trainable = False
    
    model = tf.keras.Sequential([
        base_model,
        GlobalAveragePooling2D(),
        Dense(64, activation='relu'),  # Even smaller
        Dropout(0.2),
        Dense(num_classes, activation='softmax')
    ])
    
    return model

def create_tiny_cnn_model(num_classes):
    """Create a tiny custom CNN from scratch"""
    print("🔄 Creating tiny custom CNN...")
    
    model = tf.keras.Sequential([
        # First block
        tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.MaxPooling2D(2, 2),
        
        # Second block
        tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.MaxPooling2D(2, 2),
        
        # Third block
        tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.MaxPooling2D(2, 2),
        
        # Fourth block
        tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.MaxPooling2D(2, 2),
        
        # Classifier
        tf.keras.layers.GlobalAveragePooling2D(),
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        tf.keras.layers.Dense(num_classes, activation='softmax')
    ])
    
    return model

def train_lightweight_model(model_type="mobilenet"):
    """Train a lightweight model"""
    
    # Load data
    train_datagen = ImageDataGenerator(
        rescale=1.0/255,
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        horizontal_flip=True,
        validation_split=0.2
    )
    
    train_generator = train_datagen.flow_from_directory(
        dataset_path,
        target_size=img_size,
        batch_size=batch_size,
        class_mode='categorical',
        subset='training'
    )
    
    val_generator = train_datagen.flow_from_directory(
        dataset_path,
        target_size=img_size,
        batch_size=batch_size,
        class_mode='categorical',
        subset='validation'
    )
    
    num_classes = train_generator.num_classes
    print(f"📊 Number of classes: {num_classes}")
    
    # Create model based on type
    if model_type == "mobilenet":
        model = create_mobilenet_model(num_classes)
        model_name = "model_mobilenet_lightweight.h5"
    elif model_type == "efficientnet":
        model = create_efficientnet_model(num_classes)
        model_name = "model_efficientnet_lightweight.h5"
    elif model_type == "tiny":
        model = create_tiny_cnn_model(num_classes)
        model_name = "model_tiny_cnn.h5"
    else:
        raise ValueError("model_type must be 'mobilenet', 'efficientnet', or 'tiny'")
    
    # Compile model
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Print model summary
    print("\n📋 Model Summary:")
    model.summary()
    
    # Calculate approximate size
    param_count = model.count_params()
    approx_size_mb = param_count * 4 / (1024 * 1024)  # 4 bytes per float32 parameter
    print(f"📊 Approximate model size: {approx_size_mb:.1f} MB")
    
    # Callbacks
    callbacks = [
        EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True),
        ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3, min_lr=1e-6)
    ]
    
    # Train model
    print(f"\n🚀 Training {model_type} model...")
    history = model.fit(
        train_generator,
        validation_data=val_generator,
        epochs=30,
        callbacks=callbacks,
        verbose=1
    )
    
    # Save model
    model.save(model_name)
    print(f"✅ Model saved as {model_name}")
    
    # Check actual file size
    if os.path.exists(model_name):
        actual_size = os.path.getsize(model_name) / (1024 * 1024)
        print(f"📁 Actual file size: {actual_size:.1f} MB")
    
    return model, history

def main():
    """Train all lightweight models for comparison"""
    print("🚀 Training lightweight models for deployment...\n")
    
    models_to_train = [
        ("mobilenet", "MobileNetV2 (Recommended)"),
        ("efficientnet", "EfficientNetB0 (Most Efficient)"),
        ("tiny", "Tiny CNN (Smallest)")
    ]
    
    results = {}
    
    for model_type, description in models_to_train:
        print("=" * 60)
        print(f"Training: {description}")
        print("=" * 60)
        
        try:
            model, history = train_lightweight_model(model_type)
            
            # Get final validation accuracy
            final_val_acc = max(history.history['val_accuracy'])
            results[model_type] = {
                'model': model,
                'val_accuracy': final_val_acc,
                'description': description
            }
            
            print(f"✅ {description} completed with {final_val_acc:.3f} validation accuracy\n")
            
        except Exception as e:
            print(f"❌ Error training {description}: {str(e)}\n")
    
    # Summary
    print("=" * 60)
    print("📊 TRAINING RESULTS SUMMARY")
    print("=" * 60)
    
    for model_type, result in results.items():
        print(f"{result['description']}: {result['val_accuracy']:.3f} accuracy")
    
    print("\n💡 Recommendations:")
    print("1. MobileNetV2: Best balance of size and accuracy")
    print("2. EfficientNetB0: Most parameter-efficient")
    print("3. Tiny CNN: Smallest size, may need more training")

if __name__ == "__main__":
    # Check if dataset exists
    if not os.path.exists("NN_images_processed"):
        print("❌ Dataset not found at 'NN_images_processed'")
        print("💡 Update the dataset_path variable in the script")
        print("💡 Or run this script from the correct directory")
    else:
        main()
